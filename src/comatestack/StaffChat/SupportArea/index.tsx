import styled from '@emotion/styled';
import {Flex} from 'antd';
import {useCurrentChat} from '@/regions/staff/chat';
import {Stage} from '@/components/Chat/Stage';
import {colors} from '@/constants/colors';
import {ConversationIdProvider} from '@/components/Chat/Provider/ConversationIdProvider';
import {HeaderLeft} from './HeaderLeft';
import {HeaderRight} from './HeaderRight';

const Container = styled.div`
    height: 100vh;
    display: flex;
    flex-direction: column;
`;

const Header = styled(Flex)`
    z-index: 2;
    background: ${colors.white};
    align-items: center;
    justify-content: space-between;
    height: 52px;
`;

export const SupportArea = () => {
    const {conversationId} = useCurrentChat();

    return (
        <ConversationIdProvider conversationId={conversationId}>
            <Container>
                <Header>
                    <HeaderLeft />
                    <HeaderRight />
                </Header>
                <Stage />
            </Container>
        </ConversationIdProvider>
    );
};
