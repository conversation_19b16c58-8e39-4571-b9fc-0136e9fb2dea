import {Flex, Typography} from 'antd';
import {Button} from '@panda-design/components';
import styled from '@emotion/styled';
import {useCallback} from 'react';
import {colors} from '@/constants/colors';
import {StatusIcon} from '@/design/StatusIcon';
import {StepElement, StepItem} from '@/types/staff/element';
import {setChat} from '@/regions/staff/chat';
import {EntireChatLink} from '@/links/staff';
import {toStringOrOriginal} from '@/utils/staff/convert';
import {DEFAULT_TASK_ID} from '@/constants/staff';
import {useStaffTypeIsChat} from '../Provider/StaffTypeProvider';
import {useConversationId} from '../Provider/ConversationIdProvider';

const Container = styled(Flex)`
    width: 100%;
    padding: 3px 12px 3px 16px;
    border-radius: 8px;
    color: #545454;
    background-color: ${colors['gray-3']};
`;

interface StepProps {
    item: StepItem;
}

const Step = ({item}: StepProps) => {
    const conversationId = useConversationId();
    const staffTypeIsChat = useStaffTypeIsChat();
    const {text, status, anchor} = item;
    const handleClick = useCallback(
        () => {
            if (anchor) {
                if (staffTypeIsChat) {
                    setChat(conversationId, item => ({
                        ...item,
                        currentTaskId: toStringOrOriginal(anchor?.taskId) || DEFAULT_TASK_ID,
                        currentStageId: anchor?.stageId,
                        currentTabId: anchor?.tabId,
                        currentStepId: anchor?.stepId,
                    }));
                } else {
                    window.open(
                        EntireChatLink.toUrl({
                            chatId: conversationId,
                            taskId: toStringOrOriginal(anchor?.taskId),
                            stageId: anchor?.stageId,
                            tabId: anchor?.tabId,
                            stepId: anchor?.stepId,
                        }),
                        '_blank'
                    );
                }
            }
        },
        [anchor, conversationId, staffTypeIsChat]
    );

    return (
        <Flex justify="space-between" align="center">
            <Flex gap={8} align="center" style={{flex: 1}}>
                <StatusIcon status={status} />
                <Typography.Text
                    style={{maxWidth: 'calc(100% - 20px)', fontSize: 13}}
                    ellipsis={{tooltip: true}}
                >
                    {text}
                </Typography.Text>
            </Flex>
            <Button size={'small'} type="text" onClick={handleClick}>
                详情
            </Button>
        </Flex>
    );
};

interface Props {
    item: StepElement;
}

export const ToolSteps = ({item}: Props) => {
    return (
        <Container vertical gap={8}>
            {item?.steps?.map((step, index) => (
                <Step key={step.stepId ?? index} item={step} />
            ))}
        </Container>
    );
};
