import {message} from '@panda-design/components';
import {ChatAction} from '@/types/staff/element';
import {confetti} from '@/utils/common/confetti';
import {apiPostChatActionTrigger, apiPostChatMessage} from '@/api/staff';
import {setChat} from '@/regions/staff/chat';
import {EntireChatLink} from '@/links/staff';
import {setCurrentChatId, setCurrentChatOpen} from '@/regions/staff/chatSdk';
import {toStringOrOriginal} from '@/utils/staff/convert';
import {DEFAULT_TASK_ID} from '@/constants/staff';

interface ClickContext {
    displayType: 'platform' | 'window' | 'notification' | 'app';
    conversationId: string;
    messageId: string;
    agentId: number;
    handleCloseNotification?: () => void;
}

interface ParamsClick {
    context: ClickContext;
    action: ChatAction;
}

// eslint-disable-next-line complexity
export const handleActionClick = async ({context, action}: ParamsClick) => {
    const {
        displayType,
        conversationId,
        messageId,
        agentId,
        handleCloseNotification,
    } = context;
    const {
        enableConfetti,
        query,
        anchor,
        callback,
        actionTypes = [],
    } = action;
    if (displayType === 'platform') {
        if (anchor) {
            setChat(conversationId, item => ({
                ...item,
                currentTaskId: toStringOrOriginal(anchor?.taskId) || DEFAULT_TASK_ID,
                currentStageId: anchor?.stageId,
                currentTabId: anchor?.tabId,
                currentStepId: anchor?.stepId,
            }));
        }
    } else if (actionTypes.includes('jumpPlatform')) {
        window.open(
            EntireChatLink.toUrl({
                chatId: conversationId,
                taskId: toStringOrOriginal(anchor?.taskId),
                stageId: anchor?.stageId,
                tabId: anchor?.tabId,
                stepId: anchor?.stepId,
            }),
            '_blank'
        );
    }
    if (query) {
        try {
            await apiPostChatMessage({
                agentId,
                conversationId,
                fromCallback: true,
                elements: [
                    {
                        type: 'text',
                        content: query,
                    },
                ],
            });
        } catch (error) {
            message.error('发送失败');
        }
    }
    if (callback) {
        try {
            await apiPostChatActionTrigger({
                agentId,
                conversationId,
                messageId,
                action,
            });
        } catch (error) {
            message.error('回调失败');
        }
    }
    if (enableConfetti) {
        confetti();
    }
    if (displayType === 'notification') {
        if (actionTypes.includes('openWindow')) {
            if (conversationId) {
                setCurrentChatId(conversationId);
            }
            setCurrentChatOpen(true);
        }
        if (actionTypes.includes('closeNotification')) {
            handleCloseNotification();
        }
    }
};
