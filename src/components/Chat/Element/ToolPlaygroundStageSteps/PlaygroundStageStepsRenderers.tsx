import {Flex} from 'antd';
import {DownOutlined} from '@ant-design/icons';
import styled from '@emotion/styled';
import {StageStep} from '@/types/staff/stage';
import iconsMap from '@/icons/staff';
import {Elements} from '..';
import {PlaygroundStageStepsContent} from './PlaygroundStageStepsContent';

const StepContent = styled.div`
    margin-top: 8px;
`;

const ExpandIcon = styled.div`
    color: #8c8c8c;
    font-size: 12px;
    transition: transform 0.2s;
    
    &.expanded {
        transform: rotate(0deg);
    }
    
    &.collapsed {
        transform: rotate(-180deg);
    }
`;

export function renderStepContent(step: StageStep, expanded: boolean, hasContent: boolean) {
    if (!expanded || !hasContent) {
        return null;
    }

    return (
        <StepContent>
            {step?.steps && (
                <PlaygroundStageStepsContent
                    steps={step.steps}
                    isOuterLayer={false}
                />
            )}
            {step?.elements && (
                <Flex vertical gap={8}>
                    <Elements items={step.elements} />
                </Flex>
            )}
        </StepContent>
    );
}

export function renderExpandIcon(isOuterLayer: boolean, hasContent: boolean, expanded: boolean) {
    if (!isOuterLayer || !hasContent) {
        return null;
    }

    return (
        <ExpandIcon className={expanded ? 'expanded' : 'collapsed'} style={{marginRight: '8px'}}>
            <DownOutlined />
        </ExpandIcon>
    );
}

export function renderStepIcon(step: StageStep, isOuterLayer: boolean) {
    if (isOuterLayer) {
        return null;
    }

    const IconComponent = step.icon ? iconsMap[step.icon] : null;
    return IconComponent ? <IconComponent style={{width: 16, height: 16, color: '#8c8c8c'}} /> : null;
}

export function renderInnerExpandIcon(isOuterLayer: boolean, hasContent: boolean, expanded: boolean) {
    if (isOuterLayer || !hasContent) {
        return null;
    }

    return (
        <ExpandIcon className={expanded ? 'expanded' : 'collapsed'}>
            <DownOutlined />
        </ExpandIcon>
    );
}
