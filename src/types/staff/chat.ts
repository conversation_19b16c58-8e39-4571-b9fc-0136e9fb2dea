import {ToolElement} from './element';
import {ChatStage} from './stage';

export type ChatStatusType = 'loading' | 'free';

export type ChatMessageRole = 'USER' | 'AGENT';

export interface ChatMeta {
    title: string; // 标题
    status: string; // 状态，可以参考状态对应的文档
    statusText?: string; // 可以自定义状态名
    disabled?: boolean; // 禁用对话
    disabledReason?: string; // 禁用时显示的原因
    cancelDisabled?: boolean; // 禁用取消按钮
}

export interface MessageContext {
    title: string;
}

export interface MessageTask {
    id: string;
    name: string;
    status: string;
    /* 这个字段是为了兼容单任务时的meta字段中的statusText */
    statusText?: string;
    messageId?: string;
    result?: ChatStage;
    anchorable?: boolean;
}

export interface ChatMessage {
    messageId: string;
    sender: string;
    role: ChatMessageRole;
    stream: boolean;
    finish: boolean;
    mockAwait?: number;
    meta?: ChatMeta;
    elements?: ToolElement[];
    stages?: ChatStage[];
    context?: MessageContext;
    tasks?: MessageTask[];
    source?: 'callback' | 'agent';
    canceled?: boolean;
    version?: 'v2';
}

export interface ChatHistory {
    historyId: string;
    name: string;
    status: string;
    time: string;
}

export type TaskId = string;
export interface Chat {
    conversationId: string;
    agentId: number;
    title?: string;
    username?: string;
    createTime: string;
    endTime?: string;
    delegationType: string;
    adoptStatus?: string;
    agentName?: string; // agent名称
    agentAlias?: string; // agent别名
    // 应该这样合理，每次会到达一个 Message，如果不在 messageIds 中就扔进去，然后再根据 messageId 找到对应的 Message
    // 具体看后端接口可能再调整
    messageIds?: string[];
    // 同理，动态增加 stage
    stageIds?: Record<TaskId, string[]>;
    // 一个会话里的全部任务id
    taskIds?: TaskId[];
    /* 表示当前的 taskId */
    currentTaskId?: TaskId;
    /* 表示当前的 stageId */
    currentStageId?: string;
    // 表示当前的 tabId
    currentTabId?: string;
    // 表示当前的 stepId
    currentStepId?: string;
    meta?: ChatMeta;
    status?: ChatStatusType;
    /* 这个字段用于标识agent消息是否正在发送中，status 现在使用情况不明朗，后续看是否合并一下 */
    running?: boolean;
    canceled?: boolean;
}

export interface HelloMessage {
    elements: ToolElement[];
    prompts: string[];
}

export interface Agent {
    createTime?: string;
    creator?: string;
    description?: string;
    id: number;
    name: string;
    alias?: string;
    type?: string;
    accessMethod?: string;
    helloMessage?: HelloMessage;
    iconUrl?: string;
    infoFlowAppLink?: string;
}

export interface ConversationByLabel {
    conversationId: string;
    buildStatus: string;
    adoptStatus: string;
    noticeContent: NoticeContent;
    label: string;
    agentId: number;
}
export interface NoticeContent {
    conversationId: string;
    messageId: string;
    createTime: Date;
    updateTime: Date;
    elements: ToolElement[];
}
